{"__comment__": [{"--work-thread": "tcp工作线程数量", "--ip_work-thread": "ip工作线程数, 取值的个数不应大于可用网口的个数，建议一个线程", "--rx-work-thread": "抓包线程的个数，建议一个线程", "--mbuf-per-pool": "=131071 =196607 =262143 =65535 =393215 =524287", "--converge-pkts": "1 表示将数据包进行聚合, 0 不聚合，默认使用聚合", "--rx-converge-pkts": "1表示接收数据包进行聚合, 0表示不聚合，默认使用聚合，大流量建议不聚合(比如流量大于5Gb/s)", "--ip-work-deq-num": "ip work 线程一次出队列的个数, =2048 =4096 =8192, 4G以上建议使用4096", "--rx-deq-num": "接受数据线程一次聚合处理数据包的个数，最大不能超过2048,设置的值最好是2^n", "": null}, {"upload_queue_num": "10000", "gzip_queue_num": "60000 不超过65530个 ", "split_flow_mode": "0 表示使用源IP地址和目的IP地址进行分流, 1 表示使用源IP地址、目的ip地址、源MAC地址、目的MAC地址进行分流", "ip_filter": "黑名单\n 源IP或目的IP命中者将不再解析\n IP地址格式: 127.0.0.1, ***********/24, ***********-192.168.40", "port_filter": "黑名单\n 服务器端口将不再解析\n PORT格式: 443, 9000-10000", "upload_mode": "web 网页接口\n kafka 消息队列接口\n test 不上传数据入队列测试\n 数据不入队列", "run_mode": "test 仅用于测试解析模式\n test_url 测试解析模式（输出url）\n test_no_body 测试解析模式（无BODY数据）\ntest_no_body_and_send 测试解析模式（无BODY数据，生成上传JSON）\n url 输出显示访问的url\n body 输出显示完整上传内容\n 默认不显示，全链路解析", "capd_mode": "1 IP解析与捕包在同一的线程（适用于小流量场景）\n 0 IP解析与捕包在不同的线程（适用于大流量场景）", "http_gzip_mode": "0 不解压直接上传源始数据\n 2 使用多线程解压\n 3 使用多线程解压，支持gzip嵌套", "compression_codec": "snappy none, gzip, snappy, lz4", "queue_memory_size": "2048 1024 4096", "parser_path": "以冒号区别多个加载目录，只会加载扩展名为so的文件", "upload_path": "以冒号区别多个加载目录，只会加载扩展名为so的文件", "source_path": "以冒号区别多个加载目录，只会加载扩展名为so的文件", "parser_dep": "以空格区别多条，每条以冒号分隔目标和多个依赖库，依赖库以逗号分隔。当前依赖库有tcp,ssl,http,ftp,hive等", "parser_type": "以空格区别多条，每条以冒号分隔目标和协议类型编号。当前依赖库有tcp,ssl,http,ftp,hive等", "pcap_direction": "0 抓取进入两个方向的流量，1 抓取进入网卡的流量，2 抓取网卡出去的流量", "": null}, null], "cap": {"args": ["", "--log-level=7", "--huge-dir=/mnt/huge", "--", "--verbose", "-p", "0x3", "--work-thread=4", "--ip-work-thread=1", "--rx-work-thread=1", "--mbuf-per-pool=524287", "--mbuf-data-size=9728", "--mbuf-pool-cache-size=512", "--ring-rx-size=524288", "--ring-tcp-size=524288", "--rx-converge-pkts=0", "--converge-pkts=1", "--ip-work-deq-num=2048", "--rx-deq-num=1024", null], "": null}, "plugin": {"parser_path": "parser/ssl_parser/:parser/http_parser/:parser/hive_parser/:parser/ftp_parser/:parser/mongo_parser/:parser/smb_parser", "upload_path": "upload/kafka_upload/", "source_path": "source/dpdk_source/", "load_files": "http_parser.so:dpdk_source.so:kafka_upload.so:**", "parser_dep": "mongo:tcp mysql:tcp hive:http,tcp hdfs:http,tcp ssl:tcp http:ssl,tcp hbase:tcp oracle:tcp ftp:tcp mail:tcp smb:tcp", "parser_type": "mongo:1 ssl:2 mysql:3 hive:4 hdfs:5 http:6 hbase:7 oracle:8 ftp:9 mail:10 smb:11", "parser_upstream": "http:hive http:hdfs", "": null}, "logger": {"level": "OFF", "": null}, "parser": {"tcp_worker_timeout": "30", "pfring_open_fail_reload_driver": "1", "pfring_driver_check_fail_quit": "1", "http_upload_file_size_lower_limit": "2000", "http_upload_protobuf_enable": "0", "http_drop_percent": "0", "http_drop_enable": "1", "dpdk_isolcpus": "1", "gwhw_mode": "0", "http_body_md5": "0", "use_new_event_format": "0", "inter_active_server_command_log_mode": "0", "http_host_filter": "", "http_host_white": "", "http_upload_file_types": "csv,tar,bz2,xz,jar,pdf,doc,docx,xls,ppt,xlsx,pptx,zip,txt,rar,gz,dot", "http_recv_max_size": "0", "upload_file": "1", "kafka": {"message_max_bytes": "10485760", "queue_buffering_max_messages": "100000", "queue_buffering_max_kbytes": "1048576", "compression_codec": "snappy", "batch_num_messages": "10000", "socket_keepalive_enable": "true", "acks": "1", "queue_buffering_max_ms": "1000", "socket_blocking_max_ms": "10", "reconnect_backoff_jitter_ms": "100", "connection_max_idle_ms": "30000", "socket_timeout_ms": "10000", "enable_idempotence": "0", "copy_events": "1", "brokers_list": {"http_ruled": [{"host": "kafka-server.app-audit.svc.qzprod:9093", "topic": "ApiEvents", "compression_codec": "snappy"}], "http_file": [{"host": "kafka-server.app-audit.svc.qzprod:9093", "topic": "ApiEvents", "compression_codec": "snappy"}], "http_drop_file": [{"host": "kafka-server.app-audit.svc.qzprod:9093", "topic": "DropFileEvents", "compression_codec": "snappy"}]}, "username": "root", "password": "root123", "sasl_enable": "false", "": null}, "upload_mode": "kafka", "http_ip_method_filter": [{"http_ips": "************", "http_methods": "GET"}], "need_mac_addr": "0", "run_mode": "", "capd_mode": "0", "log_filename": "/opt/upload/log.file", "ip_hosts": "1024", "tcp_streams": "10000", "http_parser_queue_num": "20000", "gzip_queue_num": "20000", "gzip_parser_queue_num": "20000", "http_upstream_queue_num": "20000", "queue_memory_size": "1024", "queue_http_parser_msg_memory_size": "1024", "queue_http_gzip_msg_memory_size": "1024", "queue_gzip_parser_msg_memory_size": "1024", "queue_http_upstream_msg_memory_size": "1024", "http_parser_mode": "1", "http_gzip_parser_mode": "1", "http_parser_thread_num": "2", "gzip_thread_num": "2", "gzip_parser_thread_num": "2", "http_upstream_mode": "0", "http_upstream_thread_num": "1", "http_request_body_max_size": "3145728", "http_response_body_max_size": "3145728", "http_url_show_size": "128", "http_body_show_size": "2048", "http_gzip_mode": "3", "http_content_type_filter": "", "http_file_type_filter": "", "http_file_download_flag": "1", "http_rsp_continue_ignore": "1", "http_pipeline_mode": "0", "http_minio_thread_num": "1", "http_minio_queue_num": "100000", "queue_http_minio_memory_size": "8192", "url_filter_base_path": "/opt/urlbase/url_filter_base.file", "ftp_upload_max_size": "2097152", "ftp_upload_queue_num": "1000", "ftp_upload_queue_memory_size": "100", "ftp_upload_thread_num": "1", "upload_thread_num": "1", "upload_queue_num": "500000", "queue_upload_msg_memory_size": "2048", "split_flow_mode": "0", "pcap_protocol": "", "pcap_port_filter": "", "pcap_port_white": "", "pcap_ip_filter": "", "pcap_ip_white": "", "pcap_ip_work_queue_num": "100000", "pcap_tcp_work_queue_num": "100000", "pcap_tcp_work_thread": "2", "port_filter": "", "port_white": "", "ip_filter": "", "ip_white": "", "limit_mbps": "", "part_limit_mbps": "", "white_part_limit_mbps": "", "rule_conf": "/opt/data/apigw/gwhw/user_info_rule.conf", "rule_forward_conf": "/opt/data/apigw/gwhw/forward_info_rule.conf", "unknown_rule_forward_max_num": "200", "session_max_num": "10000", "pcapfile_mode": "1", "pcap_timestamp": "1", "pcap_monitor_type": "dir", "pcap_dir": "/opt/pcap/task", "verbose": "1", "stats_dir": "/opt/stats/", "stats_file": "stats.file", "_analyze_dest_ip": "*************", "_analyze_source_ip": "***********", "_analyze_dest_port": "8080", "pcap_sample_mode": "0", "pcap_sample_rate": "5", "pcap_sample_dir": "/opt/sample/", "pcap_sample_queue_num": "50000", "smaple_file_num": "20", "sample_file_size": "200", "sample_core_file": "0", "sample_ip_filter": "", "sample_ip_white": "", "licutils_path": "/opt/licutils/license.lic", "unique_code": "0", "verify_license": "0", "update_license": "0", "mem_mgt": "0x1", "session_check_interval": "600", "tmp_licutils_path": "/opt/licutils/tmp/license.lic", "unique_code_path": "/opt/licutils/tmp/unique_code", "ssl_parser_mode": "0", "ssl_enable": "1", "ssl_load_engine": "0", "ssl_load_qat": "0", "ssl_queue_decrypt_thread_num": "1", "ssl_decrypt_queue_num": "5000", "ssl_queue_decrypt_memory_size": "512", "ssl_queue_data_thread_num": "1", "ssl_data_queue_num": "5000", "ssl_queue_data_memory_size": "512", "ssl_https_only": "0", "pem_dir": "/opt/data/key_files/", "ticket_dir": "/opt/data/ticket_files/", "ssl_verbose": "0x0", "ssl_session_check_interval": "120", "nic_device_name": "em1", "nic_pcap_direction": "0", "nic_ip_work_queue_num": "100000", "nic_tcp_work_queue_num": "100000", "nic_tcp_work_thread": "2", "nic_zero_copy": "1", "nic_tunnel_type": "no_tunnel", "nic_mtu": "15854,9728,1518", "gw_ip": "127.0.0.1", "insert_original_req_body": "0", "stream_debug": "0", "add_port_max_num": "50", "report_stream_interval": "3600", "session_mgt_disable_timeout_check": "1", "tcp_stream_lost_hold_min_bytes": "0", "tcp_stream_lost_hold_min_ms": "0", "tcp_nosyn_init_state": "cache", "tcp_workarounds": "1", "tcp_flow_timeout": "0", "smb_parser_thread_num": "3", "smb_parser_queue_max_num": "100000", "smb_parser_queue_memory_size": "4096", "smb_parser_upload_max_size": "5242880", "smb_parser_upload_incomplete_file": "1", "flow_enable": "0", "flow_device": "", "flow_queue_size": "", "flow_queue_mem_size": "", "flow_thread_num": "1", "flow_limit_mbps": "512", "flow_ip_white": "", "flow_ip_filter": "", "flow_port_white": "", "flow_port_filter": "", "": null}, "nacos": {"nacos_log_level": "1", "nacos_list": [{"nacos_server_addr": "nacos-server:80", "nacos_dataid_group": [{"data_id": "common.gateway_blackwhitelist.json", "group": "common", "conf_type": "filter"}, {"data_id": "auditapiv2.bootstrap.propertiess", "group": "auditapiv2", "conf_type": "mode"}, {"data_id": "gwhw.config.json", "group": "gwhw", "conf_type": "switch"}, {"data_id": "gateway_qps.json", "group": "gwhw", "conf_type": "limit"}]}]}, "minio": {"enable": "1", "endpoint": "minio-server", "access_key": "minio", "secret_key": "minio123", "buckets": "gwhw"}, "": null}