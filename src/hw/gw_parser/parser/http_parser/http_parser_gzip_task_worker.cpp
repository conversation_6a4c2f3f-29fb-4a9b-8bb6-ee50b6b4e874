/**
 * Project gw-hw
 * <AUTHOR>
 * @version 1.20.0
 */

#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <string.h>
#include <memory.h>
#include <unistd.h>
#include <arpa/inet.h>

#include "http_parser.h"
#include "http_parser_common.h"

#include "utils.h"

#include "gw_common.h"
#include "gw_logger.h"
#include "gw_config.h"
#include "session_mgt.h"
#include "session.h"

#include "http_parser_gzip_task_worker.h"

// static inline void free_http_gzip_inner(http_gzip_t *p)
// {
//   bstr_del_string(p->s);
//   cJSON_free(p->json_dump_str);
// }

thread_local thread_local_gzip_data_t CTaskWorkerGzip::m_tlgd;
thread_local z_stream CTaskWorkerGzip::m_zs;
thread_local bool CTaskWorkerGzip::m_initialized = false;

CTaskWorkerGzip::~CTaskWorkerGzip()
{
  // 确保在析构时释放 thread_local 资源
  if (m_initialized)
  {
    fini();
  }
}


int CTaskWorkerGzip::deal_data(const TaskWorkerData *ptwd)
{
  thread_local_gzip_data_t *ptlgd = &CTaskWorkerGzip::m_tlgd;

  z_streamp p_zs = &m_zs;
  inflateReset2(p_zs, MAX_WBITS + 32);
  return get_parser()->worker_routine_http_gzip_inner(ptlgd, ptwd, p_zs);
}


void CTaskWorkerGzip::free_data(const TaskWorkerData *ptwd)
{
  const http_gzip_t *p = (const http_gzip_t *)ptwd;
  get_parser()->free_http_gzip_inner(p);

  delete p;
}

void CTaskWorkerGzip::init(void)
{
  // 防止重复初始化
  if (m_initialized)
  {
    return;
  }

  const size_t buf_maxsize = HTTP_RESPONSE_BODY_MAX_LENGTH;
  const int gzip_padding = 8;
  const size_t body_buf_size = (buf_maxsize + gzip_padding);
  thread_local_gzip_data_t *ptlgd = &CTaskWorkerGzip::m_tlgd;

  // 初始化结构体
  memset(ptlgd, 0, sizeof(thread_local_gzip_data_t));
  ptlgd->buf_maxsize = buf_maxsize;
  ptlgd->body_buf_size = body_buf_size;
  ptlgd->body = new char[body_buf_size];
  memset(ptlgd->body, 0, sizeof(char) * (body_buf_size));
  ptlgd->body2 = new char[body_buf_size];
  memset(ptlgd->body2, 0, sizeof(char) * (body_buf_size));

  memset(&m_zs, 0, sizeof(z_stream));
  inflateInit2(&m_zs, MAX_WBITS + 32);
  m_initialized = true;
}

void CTaskWorkerGzip::fini(void)
{
  if (!m_initialized)
  {
    return;
  }

  thread_local_gzip_data_t *ptlgd = &CTaskWorkerGzip::m_tlgd;
  // 安全释放内存
  if (ptlgd->body != nullptr)
  {
    delete[] ptlgd->body;
    ptlgd->body = nullptr;
  }

  if (ptlgd->body2 != nullptr)
  {
    delete[] ptlgd->body2;
    ptlgd->body2 = nullptr;
  }


  inflateEnd(&m_zs);
  m_initialized = false;
}

int CTaskWorkerGzipParser::deal_data(const TaskWorkerData *ptwd)
{
  return get_parser()->http_gzip_parser_routine(ptwd);
}

void CTaskWorkerGzipParser::free_data(const TaskWorkerData *ptwd)
{
  const http_gzip_parser_t *p = (const http_gzip_parser_t*)ptwd;
  get_parser()->free_http_gzip_parser_data(p);

  return;
}