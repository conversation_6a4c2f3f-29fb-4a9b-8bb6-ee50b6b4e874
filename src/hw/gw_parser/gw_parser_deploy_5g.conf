{"cap": {"args": ["", "--log-level=7", "--huge-dir=/mnt/huge", "--", "-p=0x3", "--mbuf-per-pool=1048575", "--mbuf-pool-cache-size=512", "--ring-worker-size=8388608", "--ring-rx-size=16777216", "--mbuf-data-size=9728", "--idle-max-count=10000", "--work-thread=5"], "": null}, "parser": {"evtsvr": "http://127.0.0.1:7001/post", "kafka": {"broker": "127.0.0.1:9093", "compression_codec": "snappy", "buffering_max_messages": "20000", "topic": "ApiEvents"}, "upload_mode": "kafka", "run_mode": "", "capd_mode": "0", "upload_queue_num": "30000", "gzip_queue_num": "40000", "queue_memory_size": "4096", "ip_hosts": "102400", "tcp_streams": "100000", "gzip_thread_num": "4", "upload_thread_num": "2", "http_parser_thread_num": "8", "session_max_num": "100000", "http_url_show_size": "128", "http_body_show_size": "2048", "http_gzip_mode": "3", "ip_filter": "127.0.0.1", "port_filter": "", "unknown_rule_forward_max_num": "200", "rule_conf": "/opt/data/apigw/gwhw/user_info_rule.conf", "rule_forward_conf": "/opt/data/apigw/gwhw/forward_info_rule.conf", "http_request_body_max_size": "1048576", "http_response_body_max_size": "1048576", "pcapfile_mode": "0", "pcap_tcp_work_thread": "1", "pcap_monitor_type": "dir", "pcap_reserve": "0", "pcap_dir": "/opt/pcap/", "stats_dir": "/opt/stats/", "stats_file": "stats.file", "pcap_sample_mode": "0", "pcap_sample_rate": "5", "pcap_sample_dir": "/opt/sample/", "pcap_sample_file": "sample.pcap", "licutils_path": "/opt/licutils/license.lic", "unique_code": "0", "verify_license": "0", "update_license": "0", "tmp_licutils_path": "/opt/licutils/tmp/license.lic", "unique_code_path": "/opt/licutils/tmp/unique_code", "insert_body_value": "1", "verify_samll_version": "0", "http_parser_mode": "1", "session_check_interval": "1200", "mem_mgt": "0x1", "queue_http_parser_msg_memory_size": "2048", "http_parser_queue_num": "120000", "ssl_enable": "1", "ssl_https_only": "0", "pem_dir": "/opt/data/key_files/", "ticket_dir": "/opt/data/ticket_files/", "ssl_verbose": "0x0", "verbose": "0"}}